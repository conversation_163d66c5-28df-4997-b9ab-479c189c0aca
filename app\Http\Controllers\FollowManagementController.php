<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Organization;
use App\Models\Group;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FollowManagementController extends Controller
{
    /**
     * Show the followers management page
     */
    public function followers(Request $request)
    {
        $user = Auth::user();
        $search = $request->get('search');

        // Get followers with search functionality
        $followersQuery = $user->followers()
            ->orderBy('user_followers.created_at', 'desc');

        if ($search) {
            $followersQuery->where(function($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $followers = $followersQuery->paginate(20)->appends($request->query());

        return view('follow-management.followers', compact('followers', 'search'));
    }

    /**
     * Show the following management page with tabs
     */
    public function following(Request $request)
    {
        $user = Auth::user();
        $tab = $request->get('tab', 'users');
        $search = $request->get('search');

        $data = [
            'currentTab' => $tab,
            'search' => $search,
        ];

        switch ($tab) {
            case 'users':
                $data['following'] = $this->getFollowingUsers($user, $search, $request);
                break;
            case 'organizations':
                $data['organizations'] = $this->getFollowedOrganizations($user, $search, $request);
                break;
            case 'groups':
                $data['groups'] = $this->getFollowedGroups($user, $search, $request);
                break;
        }

        return view('follow-management.following', $data);
    }

    /**
     * Get users that the current user is following
     */
    private function getFollowingUsers(User $user, $search, Request $request)
    {
        $query = $user->following()
            ->orderBy('user_followers.created_at', 'desc');

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        return $query->paginate(20)->appends($request->query());
    }

    /**
     * Get organizations that the current user is following
     */
    private function getFollowedOrganizations(User $user, $search, Request $request)
    {
        $query = $user->followedOrganizations()
            ->orderBy('organization_followers.created_at', 'desc');

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        return $query->paginate(20)->appends($request->query());
    }

    /**
     * Get groups that the current user is a member of (since groups use membership, not following)
     */
    private function getFollowedGroups(User $user, $search, Request $request)
    {
        $query = $user->activeGroups()
            ->orderBy('group_members.joined_at', 'desc');

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        return $query->paginate(20)->appends($request->query());
    }
}
