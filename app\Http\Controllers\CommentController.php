<?php

namespace App\Http\Controllers;

use App\Models\Comment;
use App\Models\Post;
use Illuminate\Http\Request;

class CommentController extends Controller
{
    /**
     * Store a newly created comment.
     */
    public function store(Request $request, Post $post)
    {
        $validated = $request->validate([
            'content' => 'required|string|max:1000',
            'parent_id' => 'nullable|exists:comments,id',
        ]);

        $validated['user_id'] = auth()->id();
        $validated['commentable_type'] = Post::class;
        $validated['commentable_id'] = $post->id;

        $comment = Comment::create($validated);
        $comment->load('user', 'reactions');

        // Add reaction data for current user
        $comment->is_liked_by_user = $comment->isLikedBy(auth()->user());
        $comment->likes_count = $comment->likes()->count();
        $comment->user_reaction = $comment->getUserReaction(auth()->user());

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'comment' => $comment,
                'message' => 'Comment added successfully!'
            ]);
        }

        return back()->with('success', 'Comment added successfully!');
    }

    /**
     * Update the specified comment.
     */
    public function update(Request $request, Comment $comment)
    {
        // Check if user can edit this comment
        if ($comment->user_id !== auth()->id() && !auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'content' => 'required|string|max:1000',
        ]);

        $comment->update($validated);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'comment' => $comment,
                'message' => 'Comment updated successfully!'
            ]);
        }

        return back()->with('success', 'Comment updated successfully!');
    }

    /**
     * Remove the specified comment.
     */
    public function destroy(Comment $comment)
    {
        // Check if user can delete this comment
        if ($comment->user_id !== auth()->id() && !auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        $comment->delete();

        if (request()->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Comment deleted successfully!'
            ]);
        }

        return back()->with('success', 'Comment deleted successfully!');
    }

    /**
     * Get comments for a post with sorting
     */
    public function index(Request $request, Post $post)
    {
        $sortBy = $request->get('sort', 'most_relevant');

        $query = $post->comments()
            ->with(['user', 'reactions', 'replies' => function ($query) use ($sortBy) {
                $this->applySorting($query, $sortBy);
                $query->with('user', 'reactions');
            }])
            ->whereNull('parent_id');

        $this->applySorting($query, $sortBy);

        $comments = $query->get();

        // Add reaction data for current user
        $comments->each(function ($comment) {
            $comment->is_liked_by_user = $comment->isLikedBy(auth()->user());
            $comment->likes_count = $comment->likes()->count();
            $comment->user_reaction = $comment->getUserReaction(auth()->user());

            // Also add for replies
            $comment->replies->each(function ($reply) {
                $reply->is_liked_by_user = $reply->isLikedBy(auth()->user());
                $reply->likes_count = $reply->likes()->count();
                $reply->user_reaction = $reply->getUserReaction(auth()->user());
            });
        });

        return response()->json([
            'success' => true,
            'comments' => $comments,
            'sort' => $sortBy
        ]);
    }

    /**
     * Apply sorting to comment query
     */
    private function applySorting($query, $sortBy)
    {
        switch ($sortBy) {
            case 'newest':
                $query->latest('created_at');
                break;
            case 'oldest':
                $query->oldest('created_at');
                break;
            case 'most_relevant':
            default:
                // Most relevant: sort by likes count desc, then by newest
                $query->withCount('likes')
                      ->orderByDesc('likes_count')
                      ->latest('created_at');
                break;
        }
    }

    /**
     * Toggle like on a comment
     */
    public function toggleLike(Comment $comment)
    {
        $user = auth()->user();
        $like = $comment->likes()->where('user_id', $user->id)->first();

        if ($like) {
            $like->delete();
            $liked = false;
        } else {
            $comment->likes()->create(['user_id' => $user->id]);
            $liked = true;
        }

        return response()->json([
            'success' => true,
            'liked' => $liked,
            'likes_count' => $comment->likes()->count()
        ]);
    }
}
