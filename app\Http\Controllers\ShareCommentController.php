<?php

namespace App\Http\Controllers;

use App\Models\Share;
use App\Models\Comment;
use Illuminate\Http\Request;

class ShareCommentController extends Controller
{
    /**
     * Store a newly created comment on a share.
     */
    public function store(Request $request, Share $share)
    {
        $validated = $request->validate([
            'content' => 'required|string|max:1000',
            'parent_id' => 'nullable|exists:comments,id',
        ]);

        $validated['user_id'] = auth()->id();
        $validated['commentable_type'] = Share::class;
        $validated['commentable_id'] = $share->id;

        $comment = Comment::create($validated);
        $comment->load('user', 'reactions');

        // Add reaction data for current user
        $comment->is_liked_by_user = $comment->isLikedBy(auth()->user());
        $comment->likes_count = $comment->likes()->count();
        $comment->user_reaction = $comment->getUserReaction(auth()->user());

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'comment' => $comment,
                'comments_count' => $share->comments()->count()
            ]);
        }

        return back()->with('success', 'Comment added successfully!');
    }

    /**
     * Update the specified comment.
     */
    public function update(Request $request, Comment $comment)
    {
        // Check if user can edit this comment
        if ($comment->user_id !== auth()->id() && !auth()->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized action.'
            ], 403);
        }

        $validated = $request->validate([
            'content' => 'required|string|max:1000',
        ]);

        $comment->update($validated);
        $comment->load('user', 'likes');

        return response()->json([
            'success' => true,
            'comment' => $comment
        ]);
    }

    /**
     * Remove the specified comment.
     */
    public function destroy(Comment $comment)
    {
        // Check if user can delete this comment
        if ($comment->user_id !== auth()->id() && !auth()->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized action.'
            ], 403);
        }

        $shareId = $comment->commentable_id;
        $comment->delete();

        $share = Share::find($shareId);

        return response()->json([
            'success' => true,
            'message' => 'Comment deleted successfully!',
            'comments_count' => $share ? $share->comments()->count() : 0
        ]);
    }

    /**
     * Toggle like on a comment.
     */
    public function toggleLike(Comment $comment)
    {
        $user = auth()->user();
        $existingLike = $comment->likes()->where('user_id', $user->id)->first();

        if ($existingLike) {
            $existingLike->delete();
            $liked = false;
        } else {
            $comment->likes()->create(['user_id' => $user->id]);
            $liked = true;
        }

        return response()->json([
            'success' => true,
            'liked' => $liked,
            'likes_count' => $comment->likes()->count()
        ]);
    }
}
